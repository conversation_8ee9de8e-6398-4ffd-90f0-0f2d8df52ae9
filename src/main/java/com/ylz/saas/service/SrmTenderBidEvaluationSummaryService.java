package com.ylz.saas.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.entity.SrmTenderBidEvaluationSummary;

/**
 * 评标汇总表Service接口
 * <AUTHOR>
 * @createDate 2025-08-11
 */
public interface SrmTenderBidEvaluationSummaryService extends IService<SrmTenderBidEvaluationSummary> {

    /**
     * 根据评标委员会ID和报价轮次查询评标汇总信息
     * @param evaluationId 评标委员会ID
     * @param currentRound 报价轮次
     * @return 评标汇总信息
     */
    SrmTenderBidEvaluationSummary getByEvaluationIdAndRound(Long evaluationId, Integer currentRound);

    /**
     * 根据标段ID和报价轮次查询评标汇总信息
     * @param sectionId 标段ID
     * @param currentRound 报价轮次
     * @return 评标汇总信息
     */
    SrmTenderBidEvaluationSummary getBySectionIdAndRound(Long sectionId, Integer currentRound);

    /**
     * 保存或更新评标汇总信息
     * @param summary 评标汇总信息
     * @return 是否成功
     */
    boolean saveOrUpdateSummary(SrmTenderBidEvaluationSummary summary);
}
