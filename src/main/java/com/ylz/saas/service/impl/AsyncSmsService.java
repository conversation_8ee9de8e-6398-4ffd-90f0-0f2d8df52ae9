package com.ylz.saas.service.impl;

import com.ylz.saas.admin.api.dto.MessageSmsDTO;
import com.ylz.saas.admin.api.feign.RemoteMessageService;
import com.ylz.saas.common.core.util.R;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class AsyncSmsService {

    @Resource
    private RemoteMessageService remoteMessageService;

    @Async
    public void sendSmsAsync(MessageSmsDTO messageSmsDTO) {
        try {
//            R<?> result = remoteMessageService.sendSms(messageSmsDTO);
            R<?> result = remoteMessageService.sendSmsInner(messageSmsDTO);
            if (result.isOk()) {
                log.info("短信发送成功，手机号: {}, 返回信息: {}", messageSmsDTO.getMobiles(), result.getMsg());
            } else {
                log.warn("短信发送失败，手机号: {}, 错误码: {}, 业务码: {}, 错误信息: {}",
                        messageSmsDTO.getMobiles(), result.getCode(), result.getBizCode(), result.getMsg());
            }
        } catch (Exception e) {
            log.error("短信发送异常，手机号: {}", messageSmsDTO.getMobiles(), e);
        }
    }
}
