package com.ylz.saas.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.entity.SrmTenderBidEvaluationSummary;
import com.ylz.saas.mapper.SrmTenderBidEvaluationSummaryMapper;
import com.ylz.saas.service.SrmTenderBidEvaluationSummaryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 评标汇总表Service实现类
 * <AUTHOR>
 * @createDate 2025-08-11
 */
@Service
@Slf4j
public class SrmTenderBidEvaluationSummaryServiceImpl extends ServiceImpl<SrmTenderBidEvaluationSummaryMapper, SrmTenderBidEvaluationSummary>
        implements SrmTenderBidEvaluationSummaryService {

    @Override
    public SrmTenderBidEvaluationSummary getByEvaluationIdAndRound(Long evaluationId, Integer currentRound) {
        return baseMapper.getByEvaluationIdAndRound(evaluationId, currentRound);
    }

    @Override
    public SrmTenderBidEvaluationSummary getBySectionIdAndRound(Long sectionId, Integer currentRound) {
        return baseMapper.getBySectionIdAndRound(sectionId, currentRound);
    }

    @Override
    public boolean saveOrUpdateSummary(SrmTenderBidEvaluationSummary summary) {
        try {
            // 先查询是否已存在
            SrmTenderBidEvaluationSummary existing = getByEvaluationIdAndRound(
                    summary.getEvaluationId(), summary.getCurrentRound());
            
            if (existing != null) {
                // 更新现有记录
                summary.setId(existing.getId());
                return updateById(summary);
            } else {
                // 新增记录
                return save(summary);
            }
        } catch (Exception e) {
            log.error("保存或更新评标汇总信息失败", e);
            return false;
        }
    }
}
