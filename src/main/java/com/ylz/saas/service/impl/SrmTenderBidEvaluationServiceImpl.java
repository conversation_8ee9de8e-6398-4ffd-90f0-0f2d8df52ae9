package com.ylz.saas.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.common.core.exception.GlobalResultCode;
import com.ylz.saas.entity.*;
import com.ylz.saas.enums.*;
import com.ylz.saas.mapper.SrmTenderBidEvaluationMapper;
import com.ylz.saas.mapper.SrmTenderBidEvaluationScoringMapper;
import com.ylz.saas.mapper.SrmTenderEvaluationStandardMapper;
import com.ylz.saas.req.SrmTenderBidEvaluationReq;
import com.ylz.saas.req.SrmTenderEvaluationSummaryReq;
import com.ylz.saas.req.SrmTenderSectionPageReq;
import com.ylz.saas.req.SrmTenderEvaluationSummaryQueryReq;
import com.ylz.saas.req.SrmTenderOfflineEvaluationSummaryQueryReq;
import com.ylz.saas.req.SrmTenderOfflineEvaluationSummaryReq;
import com.ylz.saas.req.SrmTenderSupplierReviewDetailQueryReq;
import com.ylz.saas.req.SrmTenderLeaderSummaryReviewReq;
import com.ylz.saas.resp.SrmTenderBidEvaluationResp;
import com.ylz.saas.resp.SrmTenderSectionPageResp;
import com.ylz.saas.resp.SrmTenderEvaluationSummaryQueryResp;
import com.ylz.saas.resp.SrmTenderEvaluationSummarySupplierResp;
import com.ylz.saas.resp.SrmTenderOfflineEvaluationSummaryQueryResp;
import com.ylz.saas.resp.SrmTenderOfflineEvaluationSummarySupplierResp;
import com.ylz.saas.resp.SrmTenderSupplierReviewDetailQueryResp;
import com.ylz.saas.dto.SectionSignatureProgressDto;
import com.ylz.saas.dto.NodeScoreDto;
import com.ylz.saas.service.*;
import com.ylz.saas.common.security.util.SecurityUtils;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.Objects;

/**
 * 针对表【srm_tender_bid_evaluation(评标委员会)】的数据库操作Service实现
 * <AUTHOR>
 * @createDate 2025-07-09
 */
@Service
@Slf4j
@AllArgsConstructor
public class SrmTenderBidEvaluationServiceImpl extends ServiceImpl<SrmTenderBidEvaluationMapper, SrmTenderBidEvaluation>
        implements SrmTenderBidEvaluationService {


    private final SrmProjectMemberService srmProjectMemberService;
    private final SrmTenderSupplierResponseService srmTenderSupplierResponseService;

    private final SrmTenderNoticeService srmTenderNoticeService;
    private final SrmTenderEvaluationSignatureService srmTenderEvaluationSignatureService;
    private final SrmProcurementProjectService srmProcurementProjectService;
    private final SrmTenderBidEvaluationScoringMapper srmTenderBidEvaluationScoringMapper;
    private final SrmTenderEvaluationStandardMapper srmTenderEvaluationStandardMapper;
    private final SrmTenderOpenService srmTenderOpenService;
    private final SrmTenderEvaluationStandardService srmTenderEvaluationStandardService;
    private final SrmTenderBidEvaluationSummaryService srmTenderBidEvaluationSummaryService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBidEvaluation(SrmTenderBidEvaluationReq req) {
        // 获取当前报价轮次  不需要在评标委员会中保存轮次 只和标段绑定
//        SrmTenderOpen tenderOpen = srmTenderOpenService.lambdaQuery()
//                .eq(SrmTenderOpen::getProjectId, req.getProjectId())
//                .eq(SrmTenderOpen::getNoticeId, req.getNoticeId())
//                .eq(SrmTenderOpen::getSectionId, req.getSectionId())
//                .eq(SrmTenderOpen::getDelFlag, 0)
//                .one();
//        ExceptionUtil.check(tenderOpen == null, "500", "开标信息不存在，无法创建评标委员会");
//
//        Integer currentRound = tenderOpen.getCurrentRound();
//        ExceptionUtil.check(currentRound == null, "500", "当前报价轮次不存在");

        // 检查是否已存在相同的评标委员会记录（包含当前轮次）
        long existingCount = lambdaQuery()
                .eq(SrmTenderBidEvaluation::getProjectId, req.getProjectId())
                .eq(SrmTenderBidEvaluation::getNoticeId, req.getNoticeId())
                .eq(SrmTenderBidEvaluation::getSectionId, req.getSectionId())
//                .eq(SrmTenderBidEvaluation::getCurrentRound, currentRound)
                .eq(SrmTenderBidEvaluation::getDelFlag, 0)
                .count();

        ExceptionUtil.check(existingCount > 0, "500", "该项目标段评标委员会已存在，不允许重复创建");

        Long noticeId = req.getNoticeId();
        SrmTenderNotice byId = srmTenderNoticeService.getById(noticeId);
        ExceptionUtil.check(byId == null, "500", "招标公告不存在");

        // 保存评标委员会
        SrmTenderBidEvaluation entity = new SrmTenderBidEvaluation();
        BeanUtil.copyProperties(req, entity);
        entity.setDelFlag(0);
        entity.setTenderWay(byId.getTenderWay());
        boolean saveResult = save(entity);

        if (saveResult) {
            // 校验这个评标委员会需不需要评标，并创建对应的汇总记录
            List<SrmTenderEvaluationStandard> standards = srmTenderEvaluationStandardService.lambdaQuery()
                    .eq(SrmTenderEvaluationStandard::getProjectId, req.getProjectId())
                    .eq(SrmTenderEvaluationStandard::getNoticeId, req.getNoticeId())
                    .eq(SrmTenderEvaluationStandard::getSectionId, req.getSectionId())
                    .list();

            // 创建初始的评标汇总记录（默认第1轮）
            createInitialEvaluationSummary(entity, standards);
        }

        if (saveResult && CollectionUtil.isNotEmpty(req.getMemberList())) {
            // 保存评标委员会成员
            saveMemberList(req.getProjectId(), entity.getId(), req.getMemberList());
        }

        return saveResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBidEvaluation(SrmTenderBidEvaluationReq req) {
        // 校验当前报价轮次
        if (req.getId() != null) {
            SrmTenderBidEvaluation existing = getById(req.getId());
            ExceptionUtil.check(existing == null, "500", "评标委员会不存在");

            // 获取当前开标信息的报价轮次
            SrmTenderOpen tenderOpen = srmTenderOpenService.lambdaQuery()
                    .eq(SrmTenderOpen::getProjectId, req.getProjectId())
                    .eq(SrmTenderOpen::getNoticeId, req.getNoticeId())
                    .eq(SrmTenderOpen::getSectionId, req.getSectionId())
                    .eq(SrmTenderOpen::getDelFlag, 0)
                    .one();
            ExceptionUtil.check(tenderOpen == null, "500", "开标信息不存在");

            Integer currentRound = tenderOpen.getCurrentRound();
            ExceptionUtil.check(currentRound == null, "500", "当前报价轮次不存在");

            // 校验轮次是否匹配
//            ExceptionUtil.check(!Objects.equals(existing.getCurrentRound(), currentRound),
//                    "500", "当前报价轮次已变更，无法修改此评标委员会");
        }

        // 更新评标委员会（不更新currentRound，保持原有轮次）
        SrmTenderBidEvaluation entity = new SrmTenderBidEvaluation();
        BeanUtil.copyProperties(req, entity);
        // 确保不覆盖currentRound字段
//        entity.setCurrentRound(null);
        boolean updateResult = updateById(entity);

        if (updateResult) {
            // 重新保存成员列表
            if (CollectionUtil.isNotEmpty(req.getMemberList())) {
                saveMemberList(entity.getProjectId(), entity.getId(), req.getMemberList());
            }
        }

        return updateResult;
    }


    @Override
    public SrmTenderBidEvaluationResp getBySectionId(Long sectionId) {
        // 使用XML SQL联查获取评标委员会及其成员详细信息
        SrmTenderBidEvaluationResp resp = baseMapper.getBySectionIdAndRoundWithMembers(sectionId);

        if (resp != null) {
            // 获取当前轮次并查询汇总状态信息
            Integer currentRound = getCurrentRoundForSection(sectionId, resp.getNoticeId(), resp.getProjectId());
            if (currentRound == null) {
                currentRound = 1; // 默认第1轮
            }

            // 从新表中查询汇总状态信息
            SrmTenderBidEvaluationSummary summary = srmTenderBidEvaluationSummaryService.getByEvaluationIdAndRound(
                    resp.getId(), currentRound);
            if (summary != null) {
                resp.setSummaryStatus(summary.getSummaryStatus() != null ? summary.getSummaryStatus().name() : null);
                resp.setSummaryBy(summary.getSummaryBy());
                resp.setSummaryByName(summary.getSummaryByName());
                resp.setSummaryTime(summary.getSummaryTime());
                resp.setReportContent(summary.getReportContent());
                resp.setReportTemplateId(summary.getReportTemplateId());
                resp.setSignedReportContent(summary.getSignedReportContent());
            } else {
                // 如果没有汇总记录，设置默认状态
                resp.setSummaryStatus(EvaluationSummaryStatusEnum.PENDING.name());
            }
        }

        return resp;
    }


    /**
     * 保存评标委员会成员列表
     * @param bidEvaluationId 招标公告ID
     * @param memberList 成员列表
     */
    private void saveMemberList(Long projectId, Long bidEvaluationId, List<SrmTenderBidEvaluationReq.EvaluationMember> memberList) {
        List<SrmProjectMember> existsMemberList = srmProjectMemberService.lambdaQuery()
                .eq(SrmProjectMember::getProjectId, projectId)
                .list();

        srmProjectMemberService.lambdaUpdate()
                .eq(SrmProjectMember::getBusinessId, bidEvaluationId)
                .eq(SrmProjectMember::getMemberType, ProjectMemberTypeEnum.EVALUATION_MEMBER)
                .remove();
        List<SrmProjectMember> projectMembers = memberList.stream()
                .map(member -> {
                    SrmProjectMember projectMember = new SrmProjectMember();
                    projectMember.setProjectId(projectId);
                    projectMember.setBusinessId(bidEvaluationId);
                    projectMember.setUserId(member.getUserId());
                    projectMember.setMemberType(ProjectMemberTypeEnum.EVALUATION_MEMBER);
                    projectMember.setRole(member.getRole());
                    projectMember.setContactPhone(member.getContactPhone());
                    projectMember.setDelFlag(0);
                    return projectMember;
                })
                .collect(Collectors.toList());

        srmProjectMemberService.saveBatch(projectMembers);

        Set<Long> newUserIds = memberList.stream().map(SrmTenderBidEvaluationReq.EvaluationMember::getUserId).collect(Collectors.toSet());
        Set<Long> otherTypesUserIds = existsMemberList.stream()
                .filter(item -> !Objects.equals(ProjectMemberTypeEnum.EVALUATION_MEMBER, item.getMemberType()))
                .map(SrmProjectMember::getUserId).collect(Collectors.toSet());
        newUserIds.addAll(otherTypesUserIds);
        Set<Long> existsUserIds = existsMemberList.stream().map(SrmProjectMember::getUserId).collect(Collectors.toSet());
        srmProcurementProjectService.insertProjectUserIdentityDataPermission(srmProcurementProjectService.getById(projectId), newUserIds, existsUserIds);
    }

    @Override
    public IPage<SrmTenderSectionPageResp> pageSections(SrmTenderSectionPageReq req) {
        // 创建分页对象
        Page<SrmTenderSectionPageResp> page = new Page<>(req.getCurrent(), req.getSize());

        // 调用Mapper方法进行分页查询
        IPage<SrmTenderSectionPageResp> result = baseMapper.pageSections(page, req);

        // 批量查询签名进度
        if (!result.getRecords().isEmpty()) {
            List<Long> sectionIds = result.getRecords().stream()
                    .map(SrmTenderSectionPageResp::getSectionId)
                    .collect(Collectors.toList());

            List<SectionSignatureProgressDto> progressList =
                    baseMapper.batchGetSignatureProgress(sectionIds);

            Map<Long, String> progressMap = progressList.stream()
                    .collect(Collectors.toMap(
                            SectionSignatureProgressDto::getSectionId,
                            SectionSignatureProgressDto::getProgressText
                    ));

            // 获取当前用户ID
            Long currentUserId = null;
            try {
                currentUserId = SecurityUtils.getUser().getId();
            } catch (Exception e) {
                log.warn("获取当前用户信息失败", e);
            }

            // 设置签名进度、当前用户签名状态和操作权限
            final Long userId = currentUserId;
            result.getRecords().forEach(resp -> {
                resp.setSignatureProgress(progressMap.getOrDefault(resp.getSectionId(), "0/0"));

                // 设置当前用户是否已签名
                if (userId != null && resp.getEvaluationId() != null) {
                    boolean hasUserSigned = srmTenderEvaluationSignatureService.hasUserSigned(resp.getEvaluationId(), userId);
                    resp.setHasCurrentUserSigned(hasUserSigned);
                } else {
                    resp.setHasCurrentUserSigned(false);
                }

                setOperationPermissions(resp);
            });
        }

        return result;
    }

    /**
     * 设置操作权限
     * @param resp 响应对象
     */
    private void setOperationPermissions(SrmTenderSectionPageResp resp) {
        ExpertExtractionStatusEnum extractionStatus = resp.getExpertExtractionStatus();
        EvaluationSummaryStatusEnum summaryStatus = resp.getSummaryStatus();

        // 设置操作权限
        resp.setCanExtract(extractionStatus == ExpertExtractionStatusEnum.PENDING);
        resp.setCanViewDetail(extractionStatus == ExpertExtractionStatusEnum.EXTRACTED);
        resp.setCanReExtract(extractionStatus == ExpertExtractionStatusEnum.EXTRACTED);

        // 设置状态描述
        resp.setExpertExtractionStatusDesc(extractionStatus != null ? extractionStatus.getDesc() : "");
        resp.setSummaryStatusDesc(summaryStatus != null ? summaryStatus.getDesc() : "待汇总");

        // 如果汇总状态为空，设置默认值
        if (summaryStatus == null) {
            resp.setSummaryStatus(EvaluationSummaryStatusEnum.PENDING);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean summaryEvaluation(SrmTenderEvaluationSummaryReq req) {
        // 获取评标委员会信息
        SrmTenderBidEvaluation evaluation = getById(req.getEvaluationId());
        if (evaluation == null) {
            log.error("评标委员会不存在，ID: {}", req.getEvaluationId());
            return false;
        }

        // 创建或更新评标汇总信息到新表
        SrmTenderBidEvaluationSummary summary = new SrmTenderBidEvaluationSummary();
        summary.setProjectId(evaluation.getProjectId());
        summary.setNoticeId(evaluation.getNoticeId());
        summary.setSectionId(evaluation.getSectionId());
        summary.setEvaluationId(req.getEvaluationId());
        summary.setCurrentRound(req.getCurrentRound());
        summary.setSummaryStatus(EvaluationSummaryStatusEnum.SUMMARIZED);
        summary.setSummaryTime(LocalDateTime.now());
        summary.setReportContent(req.getReportContent());
        summary.setReportTemplateId(req.getReportTemplateId());
        summary.setSignedReportContent(req.getSignedReportContent());

        // 设置汇总人信息
        try {
            summary.setSummaryBy(SecurityUtils.getUser().getUsername());
            summary.setSummaryByName(SecurityUtils.getUser().getName());
        } catch (Exception e) {
            log.warn("获取当前用户信息失败，使用默认值: {}", e.getMessage());
            summary.setSummaryBy("system");
            summary.setSummaryByName("系统");
        }

        // 保存评标汇总信息到新表
        boolean saveResult = srmTenderBidEvaluationSummaryService.saveOrUpdateSummary(summary);
        if (!saveResult) {
            log.error("保存评标汇总信息失败，评标委员会ID: {}, 轮次: {}", req.getEvaluationId(), req.getCurrentRound());
            return false;
        }

//        更新评分状态
        srmTenderBidEvaluationScoringMapper.update(new LambdaUpdateWrapper<SrmTenderBidEvaluationScoring>()
                .eq(SrmTenderBidEvaluationScoring::getEvaluationId, evaluation.getId())
                .eq(SrmTenderBidEvaluationScoring::getCurrentRound, req.getCurrentRound())

                .set(SrmTenderBidEvaluationScoring::getStatus, SrmTenderBidEvaluationScoring.SubmitStatus.SUMMARIZED)
        );

        // 创建签名记录
        boolean createSignatureResult = srmTenderEvaluationSignatureService.createSignatureRecords(req.getEvaluationId());
        if (!createSignatureResult) {
            log.error("创建签名记录失败，评标委员会ID: {}", req.getEvaluationId());
            return false;
        }

        this.updateProjectProgressStatus(evaluation.getProjectId(), evaluation.getNoticeId());

        // 更新供应商中标候选人信息并处理评分项汇总
        if (req.getSupplierWinnerInfoList() != null && !req.getSupplierWinnerInfoList().isEmpty()) {
            boolean updateSupplierResult = updateSupplierWinnerInfoAndScoring(req);
            if (!updateSupplierResult) {
                log.error("更新供应商信息和评分项汇总失败，标段ID: {}", req.getSectionId());
                return false;
            }
        }

        return true;
    }

    /**
     * 更新供应商中标候选人信息并处理评分项汇总
     * @param req 评标汇总请求
     * @return 是否成功
     */
    private boolean updateSupplierWinnerInfoAndScoring(SrmTenderEvaluationSummaryReq req) {
        try {
            // 获取当前用户信息
            String currentUserId;
            try {
                currentUserId = SecurityUtils.getUser().getId().toString();
            } catch (Exception e) {
                log.warn("获取当前用户信息失败，使用默认值: {}", e.getMessage());
                currentUserId = "system";
            }

            // 批量更新供应商中标候选人信息并处理评分项汇总
            for (SrmTenderEvaluationSummaryReq.SupplierWinnerInfo winnerInfo : req.getSupplierWinnerInfoList()) {
                // 1. 更新中标候选人信息
                boolean updateResult = srmTenderSupplierResponseService.lambdaUpdate()
                        .set(SrmTenderSupplierResponse::getIsRecommendedWinner, winnerInfo.getIsRecommendedWinner())
                        .set(SrmTenderSupplierResponse::getWinnerCandidateOrder, winnerInfo.getWinnerCandidateOrder())
                        .eq(SrmTenderSupplierResponse::getSectionId, req.getSectionId())
                        .eq(SrmTenderSupplierResponse::getTenantSupplierId, winnerInfo.getTenantSupplierId())
                        .eq(SrmTenderSupplierResponse::getDelFlag, 0)
                        .update();

                if (!updateResult) {
                    log.error("更新供应商中标候选人信息失败，标段ID: {}, 供应商ID: {}",
                            req.getSectionId(), winnerInfo.getTenantSupplierId());
                    return false;
                }

                // 2. 处理评分项汇总（如果存在）
                if (winnerInfo.getNodeScoreSummaryList() != null && !winnerInfo.getNodeScoreSummaryList().isEmpty()) {
                    boolean scoringResult = processSupplierNodeScoring(req, winnerInfo.getTenantSupplierId(),
                            winnerInfo.getNodeScoreSummaryList(), currentUserId);
                    if (!scoringResult) {
                        log.error("处理供应商评分项汇总失败，供应商ID: {}", winnerInfo.getTenantSupplierId());
                        return false;
                    }
                }
            }
            return true;
        } catch (Exception e) {
            log.error("更新供应商信息和评分项汇总异常，标段ID: {}", req.getSectionId(), e);
            return false;
        }
    }


    private void updateProjectProgressStatus(long projectId, long noticeId) {
        boolean existsPending = this.lambdaQuery().eq(SrmTenderBidEvaluation::getProjectId, projectId)
                .eq(SrmTenderBidEvaluation::getNoticeId, noticeId)
                .eq(SrmTenderBidEvaluation::getSummaryStatus, EvaluationSummaryStatusEnum.PENDING)
                .exists();
        if (!existsPending) {
            SrmProcurementProject project = srmProcurementProjectService.getById(projectId);
            ExceptionUtil.checkNonNull(project, "项目不存在");
            srmProcurementProjectService.updateProgressStatus(project.getProjectCode(), ProjectProgressStatusEnum.EVALUATED, true);
        }
    }


    @Override
    public SrmTenderEvaluationSummaryQueryResp queryEvaluationSummary(SrmTenderEvaluationSummaryQueryReq req) {
        SrmTenderEvaluationSummaryQueryResp resp = new SrmTenderEvaluationSummaryQueryResp();

        // 1. 查询标段下所有供应商基本信息
        List<SrmTenderEvaluationSummarySupplierResp> supplierList =
                baseMapper.querySupplierBasicInfo(req.getSectionId(), req.getNoticeId(), req.getProjectId());

        // 2. 查询评标节点列表
        List<String> evaluationNodes =
                baseMapper.queryEvaluationNodes(req.getSectionId(), req.getNoticeId(), req.getProjectId());

        // 3. 为每个供应商填充详细信息
        for (SrmTenderEvaluationSummarySupplierResp supplier : supplierList) {
            // 3.1 查询评审项汇总
            String reviewSummary = baseMapper.querySupplierReviewSummary(
                    req.getSectionId(), req.getNoticeId(), req.getProjectId(), supplier.getTenantSupplierId(), req.getEvaluationId(),req.getCurrentRound());
            supplier.setReviewSummary(reviewSummary);

            // 3.2 查询各节点评分项分数
            List<NodeScoreDto> nodeScoreList = baseMapper.querySupplierNodeScores(
                    req.getSectionId(), req.getNoticeId(), req.getProjectId(), supplier.getTenantSupplierId(), req.getEvaluationId(),req.getCurrentRound());

            Map<String, BigDecimal> nodeScores = new HashMap<>();
            BigDecimal totalScore = BigDecimal.ZERO;
            for (NodeScoreDto nodeScore : nodeScoreList) {
                String nodeName = nodeScore.getNodeName();
                BigDecimal score = nodeScore.getTotalScore() != null ? nodeScore.getTotalScore() : BigDecimal.ZERO;
                nodeScores.put(nodeName + "（评分项）", score);
                totalScore = totalScore.add(score);
            }
            supplier.setNodeScores(nodeScores);
            supplier.setTotalScore(totalScore);

            // 3.3 查询投标价格
            BigDecimal bidPrice = baseMapper.querySupplierBidPrice(
                    req.getSectionId(), req.getNoticeId(), req.getProjectId(), supplier.getTenantSupplierId());
            supplier.setBidPrice(bidPrice);

            // 3.4 设置中标候选顺序描述
            if (supplier.getWinnerCandidateOrder() != null) {
                supplier.setWinnerCandidateOrderDesc(supplier.getWinnerCandidateOrder().getDesc());
            }
        }

        // 4. 计算排名（根据评审项汇总和总分进行排序）
        supplierList.sort((s1, s2) -> {
            // 首先按评审项汇总排序（符合的排在前面）
            int reviewCompare = "符合".equals(s2.getReviewSummary()) ?
                    ("符合".equals(s1.getReviewSummary()) ? 0 : 1) :
                    ("符合".equals(s1.getReviewSummary()) ? -1 : 0);
            if (reviewCompare != 0) {
                return reviewCompare;
            }
            // 然后按总分降序排序（BigDecimal比较）
            if (s1.getTotalScore() == null && s2.getTotalScore() == null) {
                return 0;
            }
            if (s1.getTotalScore() == null) {
                return 1;
            }
            if (s2.getTotalScore() == null) {
                return -1;
            }
            return s2.getTotalScore().compareTo(s1.getTotalScore());
        });

        // 设置排名
        for (int i = 0; i < supplierList.size(); i++) {
            supplierList.get(i).setRanking(i + 1);
        }

        resp.setSupplierList(supplierList);
        resp.setEvaluationNodes(evaluationNodes);

        // 5. 查询评标报告内容和汇总信息（从新的评标汇总表中查询）
        SrmTenderBidEvaluationSummary summary = srmTenderBidEvaluationSummaryService.getByEvaluationIdAndRound(
                req.getEvaluationId(), req.getCurrentRound());
        if (summary != null) {
            resp.setReportContent(summary.getReportContent());
            resp.setReportTemplateId(summary.getReportTemplateId());
            resp.setSummaryStatus(summary.getSummaryStatus() != null ? summary.getSummaryStatus().name() : null);
            resp.setSummaryBy(summary.getSummaryBy());
            resp.setSummaryByName(summary.getSummaryByName());
            resp.setSummaryTime(summary.getSummaryTime());
            resp.setEvaluationId(summary.getEvaluationId());
        } else {
            // 如果新表中没有数据，设置默认值
            resp.setEvaluationId(req.getEvaluationId());
        }

        return resp;
    }

    @Override
    public SrmTenderOfflineEvaluationSummaryQueryResp queryOfflineEvaluationSummary(SrmTenderOfflineEvaluationSummaryQueryReq req) {
        SrmTenderOfflineEvaluationSummaryQueryResp resp = new SrmTenderOfflineEvaluationSummaryQueryResp();

        // 1. 查询评标委员会信息
        SrmTenderBidEvaluation evaluation = getById(req.getEvaluationId());
        ExceptionUtil.check(evaluation == null, "500", "评标委员会不存在");

        // 2. 查询标段下所有供应商基本信息
        List<SrmTenderOfflineEvaluationSummarySupplierResp> supplierList =
                baseMapper.queryOfflineSupplierBasicInfo(req.getSectionId(), req.getNoticeId(), req.getProjectId() );

        // 3. 设置评标汇总信息（从新的评标汇总表中查询）
        resp.setEvaluationId(evaluation.getId());
        if (req.getCurrentRound() != null) {
            SrmTenderBidEvaluationSummary summary = srmTenderBidEvaluationSummaryService.getByEvaluationIdAndRound(
                    req.getEvaluationId(), req.getCurrentRound());
            if (summary != null) {
                resp.setReportContent(summary.getReportContent());
                resp.setSummaryStatus(summary.getSummaryStatus() != null ? summary.getSummaryStatus().name() : null);
                resp.setSummaryBy(summary.getSummaryBy());
                resp.setSummaryByName(summary.getSummaryByName());
                resp.setSummaryTime(summary.getSummaryTime());
            }
        }

        resp.setSupplierList(supplierList);
        return resp;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean summaryOfflineEvaluation(SrmTenderOfflineEvaluationSummaryReq req) {
        // 1. 获取评标委员会信息
        SrmTenderBidEvaluation evaluation = getById(req.getEvaluationId());
        if (evaluation == null) {
            log.error("评标委员会不存在，ID: {}", req.getEvaluationId());
            return false;
        }

        // 2. 创建或更新评标汇总信息到新表
        SrmTenderBidEvaluationSummary summary = new SrmTenderBidEvaluationSummary();
        summary.setProjectId(evaluation.getProjectId());
        summary.setNoticeId(evaluation.getNoticeId());
        summary.setSectionId(evaluation.getSectionId());
        summary.setEvaluationId(req.getEvaluationId());
        summary.setCurrentRound(req.getCurrentRound());
        summary.setSummaryStatus(EvaluationSummaryStatusEnum.SUMMARIZED);
        summary.setSummaryTime(LocalDateTime.now());
        summary.setReportContent(req.getReportContent());

        // 设置汇总人信息
        try {
            summary.setSummaryBy(SecurityUtils.getUser().getUsername());
            summary.setSummaryByName(SecurityUtils.getUser().getName());
        } catch (Exception e) {
            log.warn("获取当前用户信息失败，使用默认值: {}", e.getMessage());
            summary.setSummaryBy("system");
            summary.setSummaryByName("系统");
        }

        // 保存评标汇总信息到新表
        boolean saveResult = srmTenderBidEvaluationSummaryService.saveOrUpdateSummary(summary);
        if (!saveResult) {
            log.error("保存评标汇总信息失败，评标委员会ID: {}, 轮次: {}", req.getEvaluationId(), req.getCurrentRound());
            return false;
        }

        // 创建签名记录
        boolean createSignatureResult = srmTenderEvaluationSignatureService.createSignatureRecords(req.getEvaluationId());
        if (!createSignatureResult) {
            log.error("创建签名记录失败，评标委员会ID: {}", req.getEvaluationId());
            return false;
        }

        this.updateProjectProgressStatus(evaluation.getProjectId(), evaluation.getNoticeId());

        // 2. 更新供应商响应表中的评标信息
        if (CollectionUtil.isNotEmpty(req.getSupplierEvaluationInfoList())) {
            for (SrmTenderOfflineEvaluationSummaryReq.OfflineSupplierEvaluationInfo supplierInfo : req.getSupplierEvaluationInfoList()) {
                // 查询供应商响应记录
                LambdaQueryWrapper<SrmTenderSupplierResponse> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(SrmTenderSupplierResponse::getSectionId, req.getSectionId())
                        .eq(SrmTenderSupplierResponse::getTenantSupplierId, supplierInfo.getTenantSupplierId());

                SrmTenderSupplierResponse supplierResponse = srmTenderSupplierResponseService.getOne(queryWrapper);
                if (supplierResponse != null) {
                    // 更新中标相关信息
                    supplierResponse.setIsRecommendedWinner(supplierInfo.getIsRecommendedWinner());
                    supplierResponse.setWinnerCandidateOrder(supplierInfo.getWinnerCandidateOrder());

                    // 更新投标价格（手动输入的价格保存到quote_amount字段）
                    if (supplierInfo.getBidPrice() != null) {
                        supplierResponse.setQuoteAmount(supplierInfo.getBidPrice());
                    }

                    // 更新评标总分（手动输入的总分保存到total_score字段）
                    if (supplierInfo.getTotalScore() != null) {
                        supplierResponse.setTotalScore(supplierInfo.getTotalScore());
                    }

                    boolean updateSupplierResult = srmTenderSupplierResponseService.updateById(supplierResponse);
                    if (!updateSupplierResult) {
                        log.error("更新供应商响应信息失败，供应商ID: {}", supplierInfo.getTenantSupplierId());
                        return false;
                    }
                } else {
                    log.warn("未找到供应商响应记录，供应商ID: {}", supplierInfo.getTenantSupplierId());
                }
            }
        }

        log.info("线下评标汇总完成，评标委员会ID: {}", req.getEvaluationId());
        return true;
    }

    @Override
    public SrmTenderSupplierReviewDetailQueryResp querySupplierReviewDetail(SrmTenderSupplierReviewDetailQueryReq req) {
        SrmTenderSupplierReviewDetailQueryResp resp = new SrmTenderSupplierReviewDetailQueryResp();

        // 1. 查询供应商基本信息
        LambdaQueryWrapper<SrmTenderSupplierResponse> supplierWrapper = new LambdaQueryWrapper<>();
        supplierWrapper.eq(SrmTenderSupplierResponse::getSectionId, req.getSectionId())
                .eq(SrmTenderSupplierResponse::getNoticeId, req.getNoticeId())
                .eq(SrmTenderSupplierResponse::getProjectId, req.getProjectId())
                .eq(SrmTenderSupplierResponse::getTenantSupplierId, req.getTenantSupplierId());

        SrmTenderSupplierResponse supplier = srmTenderSupplierResponseService.getOne(supplierWrapper);
        if (supplier != null) {
            resp.setTenantSupplierId(supplier.getTenantSupplierId());
            resp.setSupplierName(supplier.getSupplierName());
        }

        // 2. 查询评审项节点列表
        List<String> reviewNodes = baseMapper.queryReviewNodes(req.getSectionId(), req.getNoticeId(), req.getProjectId());
        resp.setReviewNodes(reviewNodes);

        // 3. 查询专家评审详情
        List<SrmTenderSupplierReviewDetailQueryResp.ExpertReviewDetail> expertReviewList =
                baseMapper.queryExpertReviewDetails(req.getEvaluationId());

        // 4. 为每个专家填充节点评审结果
        for (SrmTenderSupplierReviewDetailQueryResp.ExpertReviewDetail expert : expertReviewList) {
            List<SrmTenderSupplierReviewDetailQueryResp.NodeReviewResult> nodeReviewResults =
                    baseMapper.queryExpertNodeReviewResults(req.getSectionId(), req.getNoticeId(), req.getProjectId(), req.getTenantSupplierId(), expert.getUserId(),req.getCurrentRound());
            expert.setNodeReviewResults(nodeReviewResults);
        }

        // 5. 添加组长汇总行
        List<SrmTenderSupplierReviewDetailQueryResp.NodeReviewResult> leaderSummaryResults =
                baseMapper.queryLeaderSummaryResults(req.getSectionId(), req.getNoticeId(), req.getProjectId(), req.getTenantSupplierId(),req.getCurrentRound());

        if (!leaderSummaryResults.isEmpty()) {
            SrmTenderSupplierReviewDetailQueryResp.ExpertReviewDetail leaderSummary =
                    new SrmTenderSupplierReviewDetailQueryResp.ExpertReviewDetail();
            leaderSummary.setUserId(null); // 组长汇总不关联具体用户
            leaderSummary.setExpertName("【组长汇总】");
            leaderSummary.setExpertCode("LEADER_SUMMARY");
            leaderSummary.setExpertCategory("汇总结果");
            leaderSummary.setRole("评标组长");
            leaderSummary.setNodeReviewResults(leaderSummaryResults);

            expertReviewList.add(leaderSummary);
        }

        resp.setExpertReviewList(expertReviewList);
        return resp;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean leaderSaveSummaryReview(SrmTenderLeaderSummaryReviewReq req) {
        if (CollectionUtil.isEmpty(req.getReviewItemSummaryList())) {
            log.warn("评审项汇总结果列表为空");
            return true;
        }

        // 获取当前用户信息
        String currentUserId = SecurityUtils.getUser().getId().toString();
        String currentUserName = SecurityUtils.getUser().getUsername();

        // 直接处理评审项列表，不按节点归纳
        for (SrmTenderLeaderSummaryReviewReq.ReviewItemSummary itemSummary : req.getReviewItemSummaryList()) {
            if (itemSummary.getScoringId() != null && itemSummary.getScoringId() > 0) {
                // 更新现有记录
                SrmTenderBidEvaluationScoring existingSummary = srmTenderBidEvaluationScoringMapper.selectById(itemSummary.getScoringId());
                if (existingSummary != null) {
                    existingSummary.setIsConform(itemSummary.getReviewSummaryResult());
                    existingSummary.setConclusion(itemSummary.getSummaryConclusion());
                    srmTenderBidEvaluationScoringMapper.updateById(existingSummary);
                } else {
                    log.error("组长汇总记录不存在，ID: {}", itemSummary.getScoringId());
                    return false;
                }
            } else {
                // 新增记录
                if (itemSummary.getStandardId() == null) {
                    log.error("新增组长汇总记录时，评标标准ID不能为空");
                    return false;
                }

                // 检查是否已存在相同的组长汇总记录
                SrmTenderBidEvaluationScoring existingSummary = srmTenderBidEvaluationScoringMapper.selectOne(new LambdaQueryWrapper<SrmTenderBidEvaluationScoring>().eq(SrmTenderBidEvaluationScoring::getSectionId, req.getSectionId())
                        .eq(SrmTenderBidEvaluationScoring::getNoticeId, req.getNoticeId())
                        .eq(SrmTenderBidEvaluationScoring::getProjectId, req.getProjectId())
                        .eq(SrmTenderBidEvaluationScoring::getEvaluationId, req.getEvaluationId())
                        .eq(SrmTenderBidEvaluationScoring::getTenantSupplierId, req.getTenantSupplierId())
                        .eq(SrmTenderBidEvaluationScoring::getScoringDetailId, itemSummary.getStandardId())
                        .eq(SrmTenderBidEvaluationScoring::getCurrentRound, req.getCurrentRound())
                        .eq(SrmTenderBidEvaluationScoring::getScoringType, SrmTenderBidEvaluationScoring.ScoringType.LEADER_SUMMARY));


                if (existingSummary != null) {
                    // 如果已存在，则更新
                    existingSummary.setIsConform(itemSummary.getReviewSummaryResult());
                    existingSummary.setConclusion(itemSummary.getSummaryConclusion());
                    srmTenderBidEvaluationScoringMapper.updateById(existingSummary);
                } else {
                    // 查询评标标准以确定类型
                    SrmTenderEvaluationStandard standard = srmTenderEvaluationStandardMapper.selectById(itemSummary.getStandardId());
                    if (standard == null) {
                        log.error("评标标准不存在，ID: {}", itemSummary.getStandardId());
                        return false;
                    }

                    // 创建新的组长汇总记录
                    SrmTenderBidEvaluationScoring leaderSummary = new SrmTenderBidEvaluationScoring();
                    leaderSummary.setProjectId(req.getProjectId());
                    leaderSummary.setEvaluationId(req.getEvaluationId());
                    leaderSummary.setNoticeId(req.getNoticeId());
                    leaderSummary.setSectionId(req.getSectionId());
                    leaderSummary.setUserId(Long.valueOf(currentUserId));
                    leaderSummary.setScoringDetailId(itemSummary.getStandardId());
                    leaderSummary.setTenantSupplierId(req.getTenantSupplierId());
                    leaderSummary.setIsConform(itemSummary.getReviewSummaryResult());
                    leaderSummary.setType(standard.getType()); // 根据评标标准设置类型
                    leaderSummary.setScoringType(SrmTenderBidEvaluationScoring.ScoringType.LEADER_SUMMARY);
                    leaderSummary.setConclusion(itemSummary.getSummaryConclusion());
                    leaderSummary.setStatus(SrmTenderBidEvaluationScoring.SubmitStatus.SUMMARIZED);
                    leaderSummary.setDelFlag(0);
                    leaderSummary.setCurrentRound(req.getCurrentRound());

                    srmTenderBidEvaluationScoringMapper.insert(leaderSummary);
                }
            }
        }

        // 处理节点评分项修改
        if (CollectionUtil.isNotEmpty(req.getNodeScoreSummaryList())) {
            for (SrmTenderLeaderSummaryReviewReq.NodeScoreSummary nodeScoreSummary : req.getNodeScoreSummaryList()) {
                // 查询该节点下的所有评分项标准
                List<SrmTenderEvaluationStandard> nodeStandards = srmTenderEvaluationStandardMapper.selectList(
                        new LambdaQueryWrapper<SrmTenderEvaluationStandard>()
                                .eq(SrmTenderEvaluationStandard::getSectionId, req.getSectionId())
                                .eq(SrmTenderEvaluationStandard::getNoticeId, req.getNoticeId())
                                .eq(SrmTenderEvaluationStandard::getProjectId, req.getProjectId())
                                .eq(SrmTenderEvaluationStandard::getNodeName, nodeScoreSummary.getNodeName())
                                .eq(SrmTenderEvaluationStandard::getType, ReviewEnum.SCORE)
                                .eq(SrmTenderEvaluationStandard::getDelFlag, 0)
                );

                if (nodeStandards.isEmpty()) {
                    log.warn("未找到节点 {} 下的评分项标准", nodeScoreSummary.getNodeName());
                    continue;
                }

                // 为该节点创建或更新组长汇总记录
                for (SrmTenderEvaluationStandard standard : nodeStandards) {
                    // 查询是否已存在组长汇总记录
                    SrmTenderBidEvaluationScoring existingNodeSummary = srmTenderBidEvaluationScoringMapper.selectOne(
                            new LambdaQueryWrapper<SrmTenderBidEvaluationScoring>()
                                    .eq(SrmTenderBidEvaluationScoring::getScoringDetailId, standard.getId())
                                    .eq(SrmTenderBidEvaluationScoring::getTenantSupplierId, req.getTenantSupplierId())
                                    .eq(SrmTenderBidEvaluationScoring::getEvaluationId, req.getEvaluationId())
                                    .eq(SrmTenderBidEvaluationScoring::getCurrentRound, req.getCurrentRound())
                                    .eq(SrmTenderBidEvaluationScoring::getScoringType, SrmTenderBidEvaluationScoring.ScoringType.LEADER_SUMMARY)
                                    .eq(SrmTenderBidEvaluationScoring::getDelFlag, 0)
                    );

                    if (existingNodeSummary != null) {
                        // 更新现有记录 - 按权重分配节点总分到各个评分项
                        BigDecimal itemWeight = standard.getWeight();
                        BigDecimal totalWeight = nodeStandards.stream()
                                .map(SrmTenderEvaluationStandard::getWeight)
                                .filter(Objects::nonNull)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);

                        BigDecimal itemScore = BigDecimal.ZERO;
                        if (totalWeight.compareTo(BigDecimal.ZERO) > 0) {
//                            itemScore = nodeScoreSummary.getNodeScore() * itemWeight.intValue() / totalWeight.intValue();
                            itemScore = nodeScoreSummary.getNodeScore().multiply(itemWeight).divide(totalWeight, 2, RoundingMode.HALF_UP);
                        }

                        existingNodeSummary.setScore(itemScore);
                        existingNodeSummary.setConclusion(nodeScoreSummary.getSummaryConclusion());
                        srmTenderBidEvaluationScoringMapper.updateById(existingNodeSummary);
                    } else {
                        // 创建新记录
                        BigDecimal itemWeight = standard.getWeight();
                        BigDecimal totalWeight = nodeStandards.stream()
                                .map(SrmTenderEvaluationStandard::getWeight)
                                .filter(Objects::nonNull)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);

                        BigDecimal itemScore = BigDecimal.ZERO;
                        if (totalWeight.compareTo(BigDecimal.ZERO) > 0) {
//                            itemScore = nodeScoreSummary.getNodeScore() * itemWeight.intValue() / totalWeight.intValue();
                            itemScore = nodeScoreSummary.getNodeScore().multiply(itemWeight).divide(totalWeight, 2, RoundingMode.HALF_UP);
                        }

                        SrmTenderBidEvaluationScoring nodeLeaderSummary = new SrmTenderBidEvaluationScoring();
                        nodeLeaderSummary.setProjectId(req.getProjectId());
                        nodeLeaderSummary.setEvaluationId(req.getEvaluationId());
                        nodeLeaderSummary.setNoticeId(req.getNoticeId());
                        nodeLeaderSummary.setSectionId(req.getSectionId());
                        nodeLeaderSummary.setUserId(Long.valueOf(currentUserId));
                        nodeLeaderSummary.setScoringDetailId(standard.getId());
                        nodeLeaderSummary.setTenantSupplierId(req.getTenantSupplierId());
                        nodeLeaderSummary.setScore(itemScore);
                        nodeLeaderSummary.setType(ReviewEnum.SCORE);
                        nodeLeaderSummary.setScoringType(SrmTenderBidEvaluationScoring.ScoringType.LEADER_SUMMARY);
                        nodeLeaderSummary.setConclusion(nodeScoreSummary.getSummaryConclusion());
                        nodeLeaderSummary.setStatus(SrmTenderBidEvaluationScoring.SubmitStatus.SUMMARIZED);
                        nodeLeaderSummary.setCurrentRound(req.getCurrentRound());
                        nodeLeaderSummary.setDelFlag(0);

                        srmTenderBidEvaluationScoringMapper.insert(nodeLeaderSummary);
                    }
                }
            }
        }

        srmTenderBidEvaluationScoringMapper.update(new LambdaUpdateWrapper<SrmTenderBidEvaluationScoring>().eq(SrmTenderBidEvaluationScoring::getNoticeId, req.getNoticeId())
                .eq(SrmTenderBidEvaluationScoring::getProjectId, req.getProjectId())
                .eq(SrmTenderBidEvaluationScoring::getSectionId, req.getSectionId())
                .eq(SrmTenderBidEvaluationScoring::getTenantSupplierId, req.getTenantSupplierId())
                .eq(SrmTenderBidEvaluationScoring::getEvaluationId, req.getEvaluationId())
                .eq(SrmTenderBidEvaluationScoring::getCurrentRound, req.getCurrentRound())

                .eq(SrmTenderBidEvaluationScoring::getScoringType, SrmTenderBidEvaluationScoring.ScoringType.EXPERT_SCORING)
                .eq(SrmTenderBidEvaluationScoring::getType, ReviewEnum.REVIEW)
                .set(SrmTenderBidEvaluationScoring::getStatus, SrmTenderBidEvaluationScoring.SubmitStatus.SUMMARIZED));


        log.info("组长保存汇总评审结果完成，标段ID: {}, 供应商ID: {}, 评审项数量: {}, 节点评分项数量: {}",
                req.getSectionId(), req.getTenantSupplierId(),
                req.getReviewItemSummaryList() != null ? req.getReviewItemSummaryList().size() : 0,
                req.getNodeScoreSummaryList() != null ? req.getNodeScoreSummaryList().size() : 0);
        return true;
    }


    @Override
    public boolean getNoticeEvaluationCompleteStatus(Long noticeId) {
        // 首先检查该公告下的所有标段是否都配置了评标标准
        boolean hasEvaluationStandards = srmTenderBidEvaluationScoringMapper.hasEvaluationStandards(noticeId);

        // 如果没有配置评标标准，直接返回true（认为评标已完成）
        if (!hasEvaluationStandards) {
            return true;
        }

        // 如果有配置评标标准，则检查是否所有专家都已完成评标
        return srmTenderBidEvaluationScoringMapper.countExpertEvaluationCompleted(noticeId) == 0;
    }

    /**
     * 处理单个供应商的节点评分项汇总
     * @param req 评标汇总请求
     * @param tenantSupplierId 租户供应商ID
     * @param nodeScoreSummaryList 节点评分汇总列表
     * @param currentUserId 当前用户ID
     * @return 是否成功
     */
    private boolean processSupplierNodeScoring(SrmTenderEvaluationSummaryReq req, Long tenantSupplierId,
                                               List<SrmTenderEvaluationSummaryReq.NodeScoreSummary> nodeScoreSummaryList,
                                               String currentUserId) {

        SrmTenderBidEvaluation evaluation = getById(req.getEvaluationId());

        // 处理该供应商的节点评分项汇总
        for (SrmTenderEvaluationSummaryReq.NodeScoreSummary nodeScoreSummary : nodeScoreSummaryList) {
            if (nodeScoreSummary.getNodeScore() == null) {
                continue;
            }

            // 查询该节点下的所有评分项标准
            List<SrmTenderEvaluationStandard> nodeStandards = srmTenderEvaluationStandardMapper.selectList(
                    new LambdaQueryWrapper<SrmTenderEvaluationStandard>()
                            .eq(SrmTenderEvaluationStandard::getSectionId, req.getSectionId())
                            .eq(SrmTenderEvaluationStandard::getProjectId, evaluation.getProjectId())
                            .eq(SrmTenderEvaluationStandard::getNodeName, nodeScoreSummary.getNodeName())
                            .eq(SrmTenderEvaluationStandard::getType, ReviewEnum.SCORE)
                            .eq(SrmTenderEvaluationStandard::getDelFlag, 0)
            );

            if (nodeStandards.isEmpty()) {
                log.warn("未找到节点 {} 下的评分项标准", nodeScoreSummary.getNodeName());
                continue;
            }

            // 为该节点创建或更新组长汇总记录
            boolean nodeResult = processNodeScoringSummary(req, tenantSupplierId,
                    nodeScoreSummary, nodeStandards, currentUserId);
            if (!nodeResult) {
                log.error("处理节点 {} 的评分项汇总失败，供应商ID: {}",
                        nodeScoreSummary.getNodeName(), tenantSupplierId);
                return false;
            }
        }

        return true;
    }

    /**
     * 处理单个节点的评分项汇总
     * @param req 评标汇总请求
     * @param tenantSupplierId 租户供应商ID
     * @param nodeScoreSummary 节点评分汇总信息
     * @param nodeStandards 节点下的评分项标准列表
     * @param currentUserId 当前用户ID
     * @return 是否成功
     */
    private boolean processNodeScoringSummary(SrmTenderEvaluationSummaryReq req, Long tenantSupplierId,
                                              SrmTenderEvaluationSummaryReq.NodeScoreSummary nodeScoreSummary,
                                              List<SrmTenderEvaluationStandard> nodeStandards, String currentUserId) {

        // 为该节点下的每个评分项标准创建或更新组长汇总记录
        for (SrmTenderEvaluationStandard standard : nodeStandards) {
            // 查询是否已存在组长汇总记录
            SrmTenderBidEvaluationScoring existingNodeSummary = srmTenderBidEvaluationScoringMapper.selectOne(
                    new LambdaQueryWrapper<SrmTenderBidEvaluationScoring>()
                            .eq(SrmTenderBidEvaluationScoring::getScoringDetailId, standard.getId())
                            .eq(SrmTenderBidEvaluationScoring::getTenantSupplierId, tenantSupplierId)
                            .eq(SrmTenderBidEvaluationScoring::getEvaluationId, req.getEvaluationId())
                            .eq(SrmTenderBidEvaluationScoring::getScoringType, SrmTenderBidEvaluationScoring.ScoringType.LEADER_SUMMARY)
                            .eq(SrmTenderBidEvaluationScoring::getDelFlag, 0)
            );

            // 计算该评分项的分数（按权重分配）
            BigDecimal itemWeight = standard.getWeight();
            BigDecimal totalWeight = nodeStandards.stream()
                    .map(SrmTenderEvaluationStandard::getWeight)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal itemScore = BigDecimal.ZERO;
            if (totalWeight.compareTo(BigDecimal.ZERO) > 0) {
//                itemScore = nodeScoreSummary.getNodeScore() * itemWeight.intValue() / totalWeight.intValue();
                itemScore = nodeScoreSummary.getNodeScore().multiply(itemWeight).divide(totalWeight, 2, RoundingMode.HALF_UP);

            }

            if (existingNodeSummary != null) {
                // 更新现有记录
                existingNodeSummary.setScore(itemScore);
                existingNodeSummary.setConclusion(nodeScoreSummary.getSummaryConclusion());
                boolean updateResult = srmTenderBidEvaluationScoringMapper.updateById(existingNodeSummary) > 0;
                if (!updateResult) {
                    log.error("更新组长节点汇总记录失败，ID: {}", existingNodeSummary.getId());
                    return false;
                }
            } else {
                // 创建新记录
                SrmTenderBidEvaluation evaluation = getById(req.getEvaluationId());
                SrmTenderBidEvaluationScoring nodeLeaderSummary = new SrmTenderBidEvaluationScoring();
                nodeLeaderSummary.setProjectId(evaluation.getProjectId());
                nodeLeaderSummary.setEvaluationId(req.getEvaluationId());
                nodeLeaderSummary.setNoticeId(evaluation.getNoticeId());
                nodeLeaderSummary.setSectionId(req.getSectionId());
                nodeLeaderSummary.setUserId(Long.valueOf(currentUserId));
                nodeLeaderSummary.setScoringDetailId(standard.getId());
                nodeLeaderSummary.setTenantSupplierId(tenantSupplierId);
                nodeLeaderSummary.setScore(itemScore);
                nodeLeaderSummary.setType(ReviewEnum.SCORE);
                nodeLeaderSummary.setScoringType(SrmTenderBidEvaluationScoring.ScoringType.LEADER_SUMMARY);
                nodeLeaderSummary.setConclusion(nodeScoreSummary.getSummaryConclusion());
                nodeLeaderSummary.setStatus(SrmTenderBidEvaluationScoring.SubmitStatus.SUMMARIZED);

                boolean insertResult = srmTenderBidEvaluationScoringMapper.insert(nodeLeaderSummary) > 0;
                if (!insertResult) {
                    log.error("创建组长节点汇总记录失败，标准ID: {}, 供应商ID: {}",
                            standard.getId(), tenantSupplierId);
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 创建初始的评标汇总记录
     * @param evaluation 评标委员会信息
     * @param standards 评标标准列表
     */
    private void createInitialEvaluationSummary(SrmTenderBidEvaluation evaluation, List<SrmTenderEvaluationStandard> standards) {
        try {
            // 获取当前报价轮次，如果获取不到则默认为1
            Integer currentRound = getCurrentRoundForSection(evaluation.getSectionId(), evaluation.getNoticeId(), evaluation.getProjectId());
            if (currentRound == null) {
                currentRound = 1; // 默认第1轮
                log.warn("无法获取当前报价轮次，使用默认值1，标段ID: {}", evaluation.getSectionId());
            }

            // 创建评标汇总记录
            SrmTenderBidEvaluationSummary summary = new SrmTenderBidEvaluationSummary();
            summary.setProjectId(evaluation.getProjectId());
            summary.setNoticeId(evaluation.getNoticeId());
            summary.setSectionId(evaluation.getSectionId());
            summary.setEvaluationId(evaluation.getId());
            summary.setCurrentRound(currentRound);

            // 根据是否有评标标准来设置初始汇总状态
            if (CollUtil.isEmpty(standards)) {
                // 没有评标标准，直接设置为已汇总状态
                summary.setSummaryStatus(EvaluationSummaryStatusEnum.SUMMARIZED);
                summary.setSummaryTime(LocalDateTime.now());

                // 设置汇总人信息
                try {
                    summary.setSummaryBy(SecurityUtils.getUser().getUsername());
                    summary.setSummaryByName(SecurityUtils.getUser().getName());
                } catch (Exception e) {
                    log.warn("获取当前用户信息失败，使用默认值: {}", e.getMessage());
                    summary.setSummaryBy("system");
                    summary.setSummaryByName("系统");
                }

                log.info("标段无评标标准，自动设置为已汇总状态，标段ID: {}, 轮次: {}", evaluation.getSectionId(), currentRound);
            } else {
                // 有评标标准，设置为待汇总状态
                summary.setSummaryStatus(EvaluationSummaryStatusEnum.PENDING);
                log.info("标段有评标标准，设置为待汇总状态，标段ID: {}, 轮次: {}, 标准数量: {}",
                        evaluation.getSectionId(), currentRound, standards.size());
            }

            // 保存汇总记录
            boolean saveResult = srmTenderBidEvaluationSummaryService.saveOrUpdateSummary(summary);
            if (!saveResult) {
                log.error("创建初始评标汇总记录失败，评标委员会ID: {}, 轮次: {}", evaluation.getId(), currentRound);
            }
        } catch (Exception e) {
            log.error("创建初始评标汇总记录异常，评标委员会ID: {}", evaluation.getId(), e);
        }
    }

    /**
     * 获取标段的当前报价轮次
     * @param sectionId 标段ID
     * @param noticeId 招标公告ID
     * @param projectId 项目ID
     * @return 当前轮次
     */
    private Integer getCurrentRoundForSection(Long sectionId, Long noticeId, Long projectId) {
        try {
            SrmTenderOpen tenderOpen = srmTenderOpenService.lambdaQuery()
                    .eq(SrmTenderOpen::getProjectId, projectId)
                    .eq(SrmTenderOpen::getNoticeId, noticeId)
                    .eq(SrmTenderOpen::getSectionId, sectionId)
                    .eq(SrmTenderOpen::getDelFlag, 0)
                    .one();

            return tenderOpen != null ? tenderOpen.getCurrentRound() : null;
        } catch (Exception e) {
            log.warn("获取标段当前报价轮次失败，标段ID: {}", sectionId, e);
            return null;
        }
    }

}
