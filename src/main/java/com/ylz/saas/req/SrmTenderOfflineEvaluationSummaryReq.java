package com.ylz.saas.req;

import com.ylz.saas.enums.WinnerCandidateOrderEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 线下评标汇总保存请求类
 * <AUTHOR>
 * @createDate 2025-07-14
 */
@Data
@Schema(description = "线下评标汇总保存请求类")
public class SrmTenderOfflineEvaluationSummaryReq implements Serializable {

    /**
     * 标段ID
     */
    @NotNull(message = "标段ID不能为空")
    @Schema(description = "标段ID")
    private Long sectionId;

    /**
     * 评标委员会ID
     */
    @NotNull(message = "评标委员会ID不能为空")
    @Schema(description = "评标委员会ID")
    private Long evaluationId;

    /**
     * 报价轮次
     */
    @NotNull(message = "报价轮次不能为空")
    @Schema(description = "报价轮次")
    private Integer currentRound;

    /**
     * 评标报告文件地址
     */
    @Schema(description = "评标报告文件地址")
    private String reportContent;

    /**
     * 供应商评标信息列表
     */
    @Schema(description = "供应商评标信息列表")
    private List<OfflineSupplierEvaluationInfo> supplierEvaluationInfoList;

    /**
     * 线下评标供应商信息
     */
    @Data
    @Schema(description = "线下评标供应商信息")
    public static class OfflineSupplierEvaluationInfo implements Serializable {

        /**
         * 租户供应商ID
         */
        @NotNull(message = "租户供应商ID不能为空")
        @Schema(description = "租户供应商ID")
        private Long tenantSupplierId;

        /**
         * 评标总分（手动输入）
         */
        @Schema(description = "评标总分（手动输入）")
        private BigDecimal totalScore;

        /**
         * 投标价格（手动输入）
         */
        @Schema(description = "投标价格（手动输入）")
        private BigDecimal bidPrice;

        /**
         * 是否推荐中标(0-否、1-是)
         */
        @Schema(description = "是否推荐中标(0-否、1-是)")
        private Integer isRecommendedWinner;

        /**
         * 中标候选顺序
         */
        @Schema(description = "中标候选顺序")
        private WinnerCandidateOrderEnum winnerCandidateOrder;

        private static final long serialVersionUID = 1L;
    }

    private static final long serialVersionUID = 1L;
}
