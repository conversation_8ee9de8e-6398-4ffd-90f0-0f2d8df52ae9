package com.ylz.saas.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ylz.saas.entity.SrmTenderBidEvaluationSummary;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 评标汇总表Mapper接口
 * <AUTHOR>
 * @createDate 2025-08-11
 */
@Mapper
public interface SrmTenderBidEvaluationSummaryMapper extends BaseMapper<SrmTenderBidEvaluationSummary> {

    /**
     * 根据评标委员会ID和报价轮次查询评标汇总信息
     * @param evaluationId 评标委员会ID
     * @param currentRound 报价轮次
     * @return 评标汇总信息
     */
    SrmTenderBidEvaluationSummary getByEvaluationIdAndRound(@Param("evaluationId") Long evaluationId, 
                                                           @Param("currentRound") Integer currentRound);

    /**
     * 根据标段ID和报价轮次查询评标汇总信息
     * @param sectionId 标段ID
     * @param currentRound 报价轮次
     * @return 评标汇总信息
     */
    SrmTenderBidEvaluationSummary getBySectionIdAndRound(@Param("sectionId") Long sectionId, 
                                                        @Param("currentRound") Integer currentRound);
}
