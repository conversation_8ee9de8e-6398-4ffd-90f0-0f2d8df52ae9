package com.ylz.saas.resp;

import com.ylz.saas.enums.ProjectMemberRoleEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 评标委员会响应类
 */
@Data
@Schema(description = "评标委员会响应类")
public class SrmTenderBidEvaluationResp implements Serializable {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 采购立项ID
     */
    @Schema(description = "采购立项ID")
    private Long projectId;

    /**
     * 招标公告ID
     */
    @Schema(description = "招标公告ID")
    private Long noticeId;

    /**
     * 标段ID
     */
    @Schema(description = "标段ID")
    private Long sectionId;
//
//    /**
//     * 当前报价轮次
//     */
//    @Schema(description = "当前报价轮次")
//    private Integer currentRound;

    /**
     * 评标委员会名称
     */
    @Schema(description = "评标委员会名称")
    private String name;

    /**
     * 评标地址
     */
    @Schema(description = "评标地址")
    private String address;

    /**
     * 联系人
     */
    @Schema(description = "联系人")
    private String contacts;

    /**
     * 联系人电话
     */
    @Schema(description = "联系人电话")
    private String contactsPhone;

    /**
     * 审核状态
     */
    @Schema(description = "审核状态")
    private String approveStatus;

    /**
     * 开标方式
     */
    @Schema(description = "开标方式")
    private String tenderWay;


    /**
     * 创建人名称
     */
    @Schema(description = "创建人名称")
    private String createByName;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人名称
     */
    @Schema(description = "修改人名称")
    private String updateByName;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 评标委员会成员列表
     */
    @Schema(description = "评标委员会成员列表")
    private List<EvaluationMemberResp> memberList;

    /**
     * 评标委员会成员响应
     */
    @Data
    @Schema(description = "评标委员会成员响应")
    public static class EvaluationMemberResp implements Serializable {

        /**
         * 用户ID
         */
        @Schema(description = "用户ID")
        private Long userId;

        /**
         * 用户名称
         */
        @Schema(description = "用户名称")
        private String userName;

        /**
         * 角色
         */
        @Schema(description = "角色")
        private ProjectMemberRoleEnum role;

        /**
         * 角色名称
         */
        @Schema(description = "角色名称")
        private String roleName;

        /**
         * 联系电话
         */
        @Schema(description = "联系电话")
        private String contactPhone;

        /**
         * 专家编码
         */
        @Schema(description = "专家编码")
        private String expertCode;

        /**
         * 专家姓名
         */
        @Schema(description = "专家姓名")
        private String expertName;

        /**
         * 身份证号码
         */
        @Schema(description = "身份证号码")
        private String idNumber;

        /**
         * 专家分类
         */
        @Schema(description = "专家分类")
        private String expertCategory;

        /**
         * 专家类型
         */
        @Schema(description = "专家类型")
        private String expertType;

        /**
         * 系统角色名称
         */
        @Schema(description = "系统角色名称")
        private String sysRoleName;

        private static final long serialVersionUID = 1L;
    }

    private static final long serialVersionUID = 1L;
}
