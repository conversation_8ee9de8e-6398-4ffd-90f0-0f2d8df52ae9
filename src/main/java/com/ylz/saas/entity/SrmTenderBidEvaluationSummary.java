package com.ylz.saas.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.ylz.saas.common.core.util.TenantTable;
import com.ylz.saas.enums.EvaluationSummaryStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 评标汇总表
 * @TableName srm_tender_bid_evaluation_summary
 */
@TableName(value = "srm_tender_bid_evaluation_summary")
@Data
@TenantTable
@Schema(description = "评标汇总表")
public class SrmTenderBidEvaluationSummary implements Serializable {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private Long tenantId;

    /**
     * 部门ID
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "部门ID")
    private Long deptId;

    /**
     * 采购立项ID
     */
    @Schema(description = "采购立项ID")
    private Long projectId;

    /**
     * 招标公告ID
     */
    @Schema(description = "招标公告ID")
    private Long noticeId;

    /**
     * 标段ID
     */
    @Schema(description = "标段ID")
    private Long sectionId;

    /**
     * 评标委员会ID
     */
    @Schema(description = "评标委员会ID")
    private Long evaluationId;

    /**
     * 当前报价轮次
     */
    @Schema(description = "当前报价轮次")
    private Integer currentRound;

    /**
     * 汇总状态
     */
    @Schema(description = "汇总状态")
    private EvaluationSummaryStatusEnum summaryStatus = EvaluationSummaryStatusEnum.PENDING;

    /**
     * 汇总人
     */
    @Schema(description = "汇总人")
    private String summaryBy;

    /**
     * 汇总人名称
     */
    @Schema(description = "汇总人名称")
    private String summaryByName;

    /**
     * 汇总时间
     */
    @Schema(description = "汇总时间")
    private LocalDateTime summaryTime;

    /**
     * 评标报告内容
     */
    @Schema(description = "评标报告内容")
    private String reportContent;

    /**
     * 报告模板ID
     */
    @Schema(description = "报告模板ID")
    private Long reportTemplateId;

    /**
     * 电子签章评标报告
     */
    @Schema(description = "电子签章评标报告")
    private String signedReportContent;

    /**
     * 删除标识（0-正常、1-删除）
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "删除标识（0-正常、1-删除）")
    private Integer delFlag;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建人名称
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人名称")
    private String createByName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改人名称
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改人名称")
    private String updateByName;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}
