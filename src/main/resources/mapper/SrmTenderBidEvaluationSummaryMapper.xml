<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.mapper.SrmTenderBidEvaluationSummaryMapper">

    <resultMap id="BaseResultMap" type="com.ylz.saas.entity.SrmTenderBidEvaluationSummary">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
        <result property="deptId" column="dept_id" jdbcType="BIGINT"/>
        <result property="projectId" column="project_id" jdbcType="BIGINT"/>
        <result property="noticeId" column="notice_id" jdbcType="BIGINT"/>
        <result property="sectionId" column="section_id" jdbcType="BIGINT"/>
        <result property="evaluationId" column="evaluation_id" jdbcType="BIGINT"/>
        <result property="currentRound" column="current_round" jdbcType="INTEGER"/>
        <result property="summaryStatus" column="summary_status" jdbcType="VARCHAR"/>
        <result property="summaryBy" column="summary_by" jdbcType="VARCHAR"/>
        <result property="summaryByName" column="summary_by_name" jdbcType="VARCHAR"/>
        <result property="summaryTime" column="summary_time" jdbcType="TIMESTAMP"/>
        <result property="reportContent" column="report_content" jdbcType="LONGVARCHAR"/>
        <result property="reportTemplateId" column="report_template_id" jdbcType="BIGINT"/>
        <result property="signedReportContent" column="signed_report_content" jdbcType="LONGVARCHAR"/>
        <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createByName" column="create_by_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateByName" column="update_by_name" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,dept_id,project_id,notice_id,section_id,evaluation_id,current_round,
        summary_status,summary_by,summary_by_name,summary_time,
        report_content,report_template_id,signed_report_content,
        del_flag,create_by,create_by_name,create_time,
        update_by,update_by_name,update_time
    </sql>

    <!-- 根据评标委员会ID和报价轮次查询评标汇总信息 -->
    <select id="getByEvaluationIdAndRound" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM srm_tender_bid_evaluation_summary
        WHERE evaluation_id = #{evaluationId}
          AND current_round = #{currentRound}
          AND del_flag = 0
    </select>

    <!-- 根据标段ID和报价轮次查询评标汇总信息 -->
    <select id="getBySectionIdAndRound" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM srm_tender_bid_evaluation_summary
        WHERE section_id = #{sectionId}
          AND current_round = #{currentRound}
          AND del_flag = 0
    </select>

</mapper>
