/*
 Navicat Premium Data Transfer

 Source Server         : A参盘-绿色动力
 Source Server Type    : MySQL
 Source Server Version : 50742
 Source Host           : qa-dc-saas-rds-zjk-01.dc-qa.yunlizhi.net:3306
 Source Schema         : saas_ayn_17

 Target Server Type    : MySQL
 Target Server Version : 50742
 File Encoding         : 65001

 Date: 11/08/2025 09:39:05
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for srm_tender_bid_evaluation_summary
-- ----------------------------
DROP TABLE IF EXISTS `srm_tender_bid_evaluation_summary`;
CREATE TABLE `srm_tender_bid_evaluation_summary`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  `dept_id` bigint(20) NOT NULL COMMENT '部门ID',
  `project_id` bigint(20) NOT NULL COMMENT '采购立项ID',
  `notice_id` bigint(20) NOT NULL COMMENT '招标公告ID',
  `section_id` bigint(20) NOT NULL COMMENT '标段ID',
  `del_flag` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除标识（0-正常、1-删除）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_by_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人名称',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `update_by_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人名称',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `evaluation_id` bigint(20) NOT NULL COMMENT '评标委员会ID',
  `current_round` int(11) NULL DEFAULT 0 COMMENT '当前报价轮次',
  `summary_status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'PENDING' COMMENT '汇总状态(PENDING-待汇总、SUMMARIZED-已汇总、SIGNED-已签名)',
  `summary_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '汇总人',
  `summary_by_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '汇总人名称',
  `summary_time` datetime NULL DEFAULT NULL COMMENT '汇总时间',
  `report_content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '评标报告内容',
  `report_template_id` bigint(20) NULL DEFAULT NULL COMMENT '报告模板ID',
  `signed_report_content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '电子签章评标报告',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 714 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '评标汇总表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
