-- 评标汇总数据迁移脚本
-- 将 srm_tender_bid_evaluation 表中的汇总相关字段数据迁移到 srm_tender_bid_evaluation_summary 表

-- 1. 迁移已有的评标汇总数据
-- 注意：由于原表没有 current_round 字段，我们默认设置为 1
-- 为所有评标委员会创建汇总记录，根据是否有评标标准设置初始状态
INSERT INTO srm_tender_bid_evaluation_summary (
    tenant_id,
    dept_id,
    project_id,
    notice_id,
    section_id,
    evaluation_id,
    current_round,
    summary_status,
    summary_by,
    summary_by_name,
    summary_time,
    report_content,
    report_template_id,
    signed_report_content,
    del_flag,
    create_by,
    create_by_name,
    create_time,
    update_by,
    update_by_name,
    update_time
)
SELECT
    e.tenant_id,
    e.dept_id,
    e.project_id,
    e.notice_id,
    e.section_id,
    e.id as evaluation_id,
    1 as current_round,  -- 默认设置为第1轮
    -- 根据是否有评标标准设置汇总状态
    CASE
        WHEN EXISTS (
            SELECT 1 FROM srm_tender_evaluation_standard s
            WHERE s.project_id = e.project_id
              AND s.notice_id = e.notice_id
              AND s.section_id = e.section_id
              AND s.del_flag = 0
        ) THEN COALESCE(e.summary_status, 'PENDING')
        ELSE 'SUMMARIZED'
    END as summary_status,
    CASE
        WHEN EXISTS (
            SELECT 1 FROM srm_tender_evaluation_standard s
            WHERE s.project_id = e.project_id
              AND s.notice_id = e.notice_id
              AND s.section_id = e.section_id
              AND s.del_flag = 0
        ) THEN e.summary_by
        ELSE COALESCE(e.summary_by, 'system')
    END as summary_by,
    CASE
        WHEN EXISTS (
            SELECT 1 FROM srm_tender_evaluation_standard s
            WHERE s.project_id = e.project_id
              AND s.notice_id = e.notice_id
              AND s.section_id = e.section_id
              AND s.del_flag = 0
        ) THEN e.summary_by_name
        ELSE COALESCE(e.summary_by_name, '系统')
    END as summary_by_name,
    CASE
        WHEN EXISTS (
            SELECT 1 FROM srm_tender_evaluation_standard s
            WHERE s.project_id = e.project_id
              AND s.notice_id = e.notice_id
              AND s.section_id = e.section_id
              AND s.del_flag = 0
        ) THEN e.summary_time
        ELSE COALESCE(e.summary_time, e.create_time)
    END as summary_time,
    e.report_content,
    e.report_template_id,
    e.signed_report_content,
    e.del_flag,
    e.create_by,
    e.create_by_name,
    e.create_time,
    e.update_by,
    e.update_by_name,
    e.update_time
FROM srm_tender_bid_evaluation e
WHERE e.del_flag = 0
  AND NOT EXISTS (
    -- 避免重复插入
    SELECT 1 FROM srm_tender_bid_evaluation_summary s
    WHERE s.evaluation_id = e.id AND s.current_round = 1
  );

-- 2. 验证迁移结果
-- 查询迁移的记录数量
SELECT
    '迁移完成' as status,
    COUNT(*) as migrated_count,
    COUNT(CASE WHEN summary_status = 'SUMMARIZED' THEN 1 END) as summarized_count,
    COUNT(CASE WHEN summary_status = 'PENDING' THEN 1 END) as pending_count,
    COUNT(CASE WHEN report_content IS NOT NULL THEN 1 END) as with_report_count
FROM srm_tender_bid_evaluation_summary
WHERE current_round = 1;

-- 3. 数据一致性检查
-- 检查评标委员会总数与汇总记录总数是否一致
SELECT
    '一致性检查' as check_type,
    (SELECT COUNT(*) FROM srm_tender_bid_evaluation WHERE del_flag = 0) as evaluation_count,
    (SELECT COUNT(*) FROM srm_tender_bid_evaluation_summary WHERE current_round = 1) as summary_count,
    CASE
        WHEN (SELECT COUNT(*) FROM srm_tender_bid_evaluation WHERE del_flag = 0) =
             (SELECT COUNT(*) FROM srm_tender_bid_evaluation_summary WHERE current_round = 1)
        THEN '一致'
        ELSE '不一致'
    END as consistency_status;

-- 4. 检查无评标标准的委员会是否正确设置为SUMMARIZED状态
SELECT
    '无标准委员会检查' as check_type,
    COUNT(*) as no_standard_count,
    COUNT(CASE WHEN s.summary_status = 'SUMMARIZED' THEN 1 END) as auto_summarized_count
FROM srm_tender_bid_evaluation e
LEFT JOIN srm_tender_bid_evaluation_summary s ON e.id = s.evaluation_id AND s.current_round = 1
WHERE e.del_flag = 0
  AND NOT EXISTS (
    SELECT 1 FROM srm_tender_evaluation_standard st
    WHERE st.project_id = e.project_id
      AND st.notice_id = e.notice_id
      AND st.section_id = e.section_id
      AND st.del_flag = 0
  );

-- 4. 清理脚本（可选，在确认迁移成功后执行）
-- 注意：这个脚本会删除原表中的汇总相关字段数据，请谨慎执行
/*
-- 清理原表中的汇总相关字段（保留表结构，只清空数据）
UPDATE srm_tender_bid_evaluation 
SET 
    summary_status = NULL,
    summary_by = NULL,
    summary_by_name = NULL,
    summary_time = NULL,
    report_content = NULL,
    report_template_id = NULL,
    signed_report_content = NULL,
    update_time = CURRENT_TIMESTAMP
WHERE del_flag = 0
  AND (
    summary_status IS NOT NULL 
    OR report_content IS NOT NULL 
    OR report_template_id IS NOT NULL 
    OR signed_report_content IS NOT NULL
  );
*/

-- 5. 回滚脚本（如果需要回滚迁移）
/*
-- 回滚：将数据从新表迁移回原表
UPDATE srm_tender_bid_evaluation e
INNER JOIN srm_tender_bid_evaluation_summary s ON e.id = s.evaluation_id AND s.current_round = 1
SET 
    e.summary_status = s.summary_status,
    e.summary_by = s.summary_by,
    e.summary_by_name = s.summary_by_name,
    e.summary_time = s.summary_time,
    e.report_content = s.report_content,
    e.report_template_id = s.report_template_id,
    e.signed_report_content = s.signed_report_content,
    e.update_time = CURRENT_TIMESTAMP
WHERE e.del_flag = 0;

-- 删除新表中的迁移数据
DELETE FROM srm_tender_bid_evaluation_summary WHERE current_round = 1;
*/
