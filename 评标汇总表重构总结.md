# 评标汇总表重构总结

## 概述
本次重构将评标汇总相关字段从 `srm_tender_bid_evaluation` 表迁移到新的 `srm_tender_bid_evaluation_summary` 表中，以支持不同报价轮次下的评标汇总信息存储。

## 新增文件

### 1. 实体类和数据访问层
- `src/main/java/com/ylz/saas/entity/SrmTenderBidEvaluationSummary.java` - 评标汇总表实体类
- `src/main/java/com/ylz/saas/mapper/SrmTenderBidEvaluationSummaryMapper.java` - Mapper接口
- `src/main/resources/mapper/SrmTenderBidEvaluationSummaryMapper.xml` - XML映射文件

### 2. 服务层
- `src/main/java/com/ylz/saas/service/SrmTenderBidEvaluationSummaryService.java` - Service接口
- `src/main/java/com/ylz/saas/service/impl/SrmTenderBidEvaluationSummaryServiceImpl.java` - Service实现类

### 3. 数据迁移脚本
- `src/main/resources/db/migration/migrate_evaluation_summary_data.sql` - 数据迁移脚本

## 修改的文件

### 1. 实体类修改
**SrmTenderBidEvaluation.java**
- 移除字段：`summaryStatus`, `summaryBy`, `summaryByName`, `summaryTime`, `reportContent`, `reportTemplateId`, `signedReportContent`
- 保留字段：`tenderWay`（开标方式）

### 2. 响应类修改
**SrmTenderBidEvaluationResp.java**
- 重新添加字段：`summaryStatus`, `summaryBy`, `summaryByName`, `summaryTime`, `reportContent`, `reportTemplateId`, `signedReportContent`（从新表中查询）
- 保留字段：`tenderWay`（开标方式）

### 3. 请求类修改
**SrmTenderOfflineEvaluationSummaryReq.java**
- 新增字段：`currentRound`（报价轮次）

### 4. Mapper XML修改
**SrmTenderBidEvaluationMapper.xml**
- 更新 `Base_Column_List`：移除汇总相关字段，添加 `tender_way`
- 更新 `BaseResultMap`：移除汇总相关字段映射，添加 `tenderWay` 映射
- 更新 `EvaluationWithMembersResultMap`：移除汇总相关字段映射
- 更新查询语句：移除汇总相关字段的查询

### 5. 服务层修改
**SrmTenderBidEvaluationServiceImpl.java**
- 注入新的 `SrmTenderBidEvaluationSummaryService`
- 修改 `saveBidEvaluation` 方法：兼容评标标准逻辑，自动创建初始汇总记录
- 修改 `summaryEvaluation` 方法：将汇总信息保存到新表
- 修改 `queryEvaluationSummary` 方法：从新表查询汇总信息
- 修改 `summaryOfflineEvaluation` 方法：将线下评标汇总信息保存到新表
- 修改 `queryOfflineEvaluationSummary` 方法：从新表查询线下评标汇总信息
- 修改 `getBySectionId` 方法：从新表查询汇总状态信息
- 新增 `createInitialEvaluationSummary` 方法：根据评标标准自动设置初始汇总状态
- 新增 `getCurrentRoundForSection` 方法：获取标段的当前报价轮次

## 新表结构

### srm_tender_bid_evaluation_summary 表字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 主键ID |
| tenant_id | bigint | 租户ID |
| dept_id | bigint | 部门ID |
| project_id | bigint | 采购立项ID |
| notice_id | bigint | 招标公告ID |
| section_id | bigint | 标段ID |
| evaluation_id | bigint | 评标委员会ID |
| current_round | int | 当前报价轮次 |
| summary_status | varchar(50) | 汇总状态 |
| summary_by | varchar(64) | 汇总人 |
| summary_by_name | varchar(64) | 汇总人名称 |
| summary_time | datetime | 汇总时间 |
| report_content | longtext | 评标报告内容 |
| report_template_id | bigint | 报告模板ID |
| signed_report_content | longtext | 电子签章评标报告 |
| del_flag | tinyint | 删除标识 |
| create_by | varchar(64) | 创建人 |
| create_by_name | varchar(64) | 创建人名称 |
| create_time | datetime | 创建时间 |
| update_by | varchar(64) | 修改人 |
| update_by_name | varchar(64) | 修改人名称 |
| update_time | datetime | 修改时间 |

## 数据迁移方案

### 1. 迁移策略
- 将现有的评标汇总数据从 `srm_tender_bid_evaluation` 表迁移到 `srm_tender_bid_evaluation_summary` 表
- 由于原表没有轮次字段，默认设置为第1轮（`current_round = 1`）
- 只迁移包含汇总信息的记录（至少有一个汇总相关字段不为空）

### 2. 迁移步骤
1. 执行迁移脚本：`src/main/resources/db/migration/migrate_evaluation_summary_data.sql`
2. 验证迁移结果：检查迁移的记录数量和数据完整性
3. 测试新接口：确保评标汇总功能正常工作
4. 可选：清理原表中的汇总字段数据（脚本中已提供，但被注释）

### 3. 回滚方案
如果需要回滚，可以使用迁移脚本中提供的回滚SQL，将数据从新表迁移回原表。

## 接口变更

### 1. 评标汇总保存接口
- 接口：`POST /srmTenderBidEvaluation/summaryEvaluation`
- 变更：汇总信息保存到新表 `srm_tender_bid_evaluation_summary`

### 2. 评标汇总查询接口
- 接口：`POST /srmTenderBidEvaluation/queryEvaluationSummary`
- 变更：从新表 `srm_tender_bid_evaluation_summary` 查询汇总信息

### 3. 线下评标汇总保存接口
- 接口：`POST /srmTenderBidEvaluation/summaryOfflineEvaluation`
- 变更：请求参数新增 `currentRound` 字段，汇总信息保存到新表

### 4. 线下评标汇总查询接口
- 接口：`POST /srmTenderBidEvaluation/queryOfflineEvaluationSummary`
- 变更：从新表查询汇总信息（如果提供了轮次参数）

## 评标标准兼容逻辑

### 核心逻辑
在创建评标委员会时，系统会自动检查该标段是否配置了评标标准：

1. **有评标标准**：设置汇总状态为 `PENDING`（待汇总），需要手动进行评标汇总
2. **无评标标准**：设置汇总状态为 `SUMMARIZED`（已汇总），自动完成汇总

### 实现方式
- `saveBidEvaluation` 方法中调用 `createInitialEvaluationSummary` 方法
- 查询 `srm_tender_evaluation_standard` 表判断是否有评标标准
- 根据结果自动设置初始汇总状态和汇总时间
- 数据迁移脚本也包含了相同的逻辑，确保存量数据的正确性

## 注意事项

1. **向后兼容性**：新接口保持了与原接口相同的参数结构，只是内部实现改变
2. **数据完整性**：迁移脚本包含了数据一致性检查，确保迁移的完整性
3. **轮次支持**：新表支持多轮次评标汇总，为未来的多轮报价功能提供了基础
4. **错误处理**：如果新表中没有数据，查询接口会返回默认值，保证系统稳定性
5. **评标标准兼容**：完全兼容原有的评标标准判断逻辑，无评标标准的委员会自动设置为已汇总状态

## 测试建议

1. **单元测试**：测试新的Service方法
2. **集成测试**：测试完整的评标汇总流程
3. **数据迁移测试**：在测试环境验证数据迁移的正确性
4. **接口测试**：确保前端调用不受影响
